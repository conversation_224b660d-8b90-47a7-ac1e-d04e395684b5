{"name": "tasktakr-web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "axios": "^1.9.0", "next-pwa": "^5.6.0", "@reduxjs/toolkit": "^2.0.0", "react-redux": "^9.0.0"}, "devDependencies": {"typescript": "^5.3.0", "@types/react": "^18.2.0", "@types/node": "^20.0.0", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0"}}