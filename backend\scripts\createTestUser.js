const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

const createTestUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/tasktakr');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Test user already exists');
      process.exit(0);
    }

    // Create test user
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    });

    console.log('Test user created successfully:', testUser.email);
    console.log('Default password: password123');
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
};

createTestUser();