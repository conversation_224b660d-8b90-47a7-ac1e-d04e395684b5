# TaskTakr API URL Configuration Guide

## Overview
This document explains the centralized API URL configuration system implemented to resolve inconsistent API URLs across the TaskTakr project.

## Problem Solved
Previously, the codebase had inconsistent API URLs scattered throughout different files:
- React Native app used `http://backend.tasktakr.in:5099/api`
- Web app had hardcoded `http://backend.tasktakr.in:5099/api`
- Socket service used `http://backend.tasktakr.in:5099`
- Some screens had hardcoded localhost URLs
- Vercel configuration pointed to wrong endpoints

## Solution Implemented

### 1. Centralized Configuration (React Native)
**File: `src/config.js`**
- Environment-based API URL configuration
- Platform-specific settings (web vs mobile)
- Development vs Production environment detection
- Configurable endpoints for different deployment scenarios

### 2. Web Application Configuration
**File: `web/services/api.ts`**
- Environment variable-based configuration
- Uses `NEXT_PUBLIC_API_URL` for runtime configuration
- Fallback to default URLs if environment variables not set

**File: `web/services/socket.ts`**
- Environment variable-based socket URL configuration
- Uses `NEXT_PUBLIC_SOCKET_URL` for runtime configuration

### 3. Environment Files
**Files:**
- `web/.env.local` - Development environment
- `web/.env.production` - Production environment
- `web/.env.example` - Template for developers

### 4. Deployment Configuration
**File: `web/vercel.json`**
- Updated to use correct backend URLs
- Environment variables properly configured for production

## Configuration Details

### React Native (Mobile App)
```javascript
// Development URLs
Android Emulator: http://********:5099/api
Physical Device: http://***********:5099/api (update IP as needed)

// Production URLs
Production: https://backend.tasktakr.in/api
```

### Web Application
```javascript
// Development URLs
Development: http://localhost:5099/api

// Production URLs
Production: https://backend.tasktakr.in/api
```

### Socket Connections
```javascript
// Development URLs
Development: http://localhost:5099

// Production URLs
Production: https://backend.tasktakr.in
```

## Environment Variables

### Web Application (.env files)
```bash
# Development
NEXT_PUBLIC_API_URL=http://localhost:5099/api
NEXT_PUBLIC_SOCKET_URL=http://localhost:5099
NODE_ENV=development

# Production
NEXT_PUBLIC_API_URL=https://backend.tasktakr.in/api
NEXT_PUBLIC_SOCKET_URL=https://backend.tasktakr.in
NODE_ENV=production
```

## Usage Instructions

### For Development
1. **React Native**: Update IP address in `src/config.js` if using physical device
2. **Web**: Copy `web/.env.example` to `web/.env.local` and update URLs if needed
3. **Backend**: Ensure backend server is running on port 5099

### For Production Deployment
1. **React Native**: No changes needed - automatically uses production URLs
2. **Web**: Environment variables are set in `web/vercel.json` for Vercel deployment
3. **Backend**: Deploy backend to `backend.tasktakr.in`

## Files Modified
1. `src/config.js` - Centralized React Native configuration
2. `src/screens/LoginScreen.js` - Updated to use centralized config
3. `src/screens/SignupScreen.js` - Updated to use centralized config
4. `src/screens/HomeScreen.js` - Fixed health check endpoint
5. `web/services/api.ts` - Environment-based configuration
6. `web/services/socket.ts` - Environment-based socket configuration
7. `web/.env.local` - Development environment variables
8. `web/.env.production` - Production environment variables
9. `web/.env.example` - Template for developers
10. `web/vercel.json` - Updated deployment configuration

## Benefits
1. **Consistency**: All API URLs are now centrally managed
2. **Environment Awareness**: Automatic switching between dev/prod URLs
3. **Maintainability**: Easy to update URLs in one place
4. **Flexibility**: Support for different deployment scenarios
5. **Developer Experience**: Clear documentation and examples

## Troubleshooting

### React Native Development
- **Android Emulator**: Use `********:5099` (default)
- **Physical Device**: Update IP in `src/config.js` to your computer's local IP
- **iOS Simulator**: Use `localhost:5099`

### Web Development
- Ensure `.env.local` exists with correct URLs
- Check that backend server is running on specified port
- Verify CORS settings allow your development domain

### Production Issues
- Verify backend is accessible at `backend.tasktakr.in`
- Check SSL certificates for HTTPS endpoints
- Ensure environment variables are properly set in deployment platform
