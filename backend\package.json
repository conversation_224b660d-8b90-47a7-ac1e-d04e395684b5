{"name": "tasktakr-backend", "version": "1.0.0", "description": "TaskTakr backend server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"@googlemaps/polyline-codec": "^1.0.28", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.1", "expo-server-sdk": "^3.7.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "socket.io": "^4.8.1", "validator": "^13.11.0"}, "devDependencies": {"nodemon": "^3.0.3"}}