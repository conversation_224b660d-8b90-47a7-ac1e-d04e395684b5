{"expo": {"name": "TaskTakr", "slug": "tasktakr", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.tasktakr.app", "buildNumber": "1.0.0", "runtimeVersion": {"policy": "appVersion"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.tasktakr.app", "versionCode": 1, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "NOTIFICATIONS", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK", "ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "NOTIFICATIONS", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"], "runtimeVersion": "1.0.0"}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-task-manager", "expo-background-fetch"], "extra": {"eas": {"projectId": "7f06c211-aab8-4723-ad39-edf9485e023c"}}, "owner": "<PERSON><PERSON><PERSON>", "updates": {"url": "https://u.expo.dev/7f06c211-aab8-4723-ad39-edf9485e023c"}}}