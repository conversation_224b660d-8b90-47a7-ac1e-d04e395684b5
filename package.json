{"name": "tasktakr", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/geolocation": "^3.1.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.2", "axios": "^1.9.0", "expo": "~53.0.9", "expo-background-fetch": "~13.1.5", "expo-location": "~18.1.5", "expo-status-bar": "~2.2.3", "expo-task-manager": "~13.1.5", "react": "19.0.0", "react-native": "0.79.2", "react-native-calendars": "^1.1312.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.25.0", "react-native-maps": "^1.20.1", "react-native-paper": "^5.14.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "expo-dev-client": "~5.1.8", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.13"}, "devDependencies": {"@babel/core": "^7.20.0", "typescript": "~5.8.3"}, "private": true}