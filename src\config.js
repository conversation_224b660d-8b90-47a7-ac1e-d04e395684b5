import { Platform } from 'react-native';

// Environment detection
const isDevelopment = __DEV__;
const isProduction = !__DEV__;

// API Configuration based on environment
const getApiConfig = () => {
  if (Platform.OS === 'web') {
    // Web platform configuration
    if (isProduction) {
      return {
        BASE_URL: 'https://backend.tasktakr.in/api',
        SOCKET_URL: 'https://backend.tasktakr.in'
      };
    } else {
      return {
        BASE_URL: 'http://localhost:5099/api',
        SOCKET_URL: 'http://localhost:5099'
      };
    }
  } else {
    // React Native platform configuration
    if (isProduction) {
      return {
        BASE_URL: 'https://backend.tasktakr.in/api',
        SOCKET_URL: 'https://backend.tasktakr.in'
      };
    } else {
      // For development, use localhost for emulator or your local IP for physical device
      return {
        BASE_URL: 'http://********:5099/api', // Android emulator
        // BASE_URL: 'http://***********:5099/api', // Uncomment and update IP for physical device
        SOCKET_URL: 'http://********:5099'
        // SOCKET_URL: 'http://***********:5099' // Uncomment and update IP for physical device
      };
    }
  }
};

const config = getApiConfig();

// Export API configuration
export const API_URL = config.BASE_URL;
export const SOCKET_URL = config.SOCKET_URL;

// API endpoints (relative paths)
export const ENDPOINTS = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout'
  },
  services: '/services',
  bookings: '/bookings',
  providers: '/providers',
  users: '/users',
  payments: '/payments',
  health: '/health'
};

// API request timeout (in milliseconds)
export const REQUEST_TIMEOUT = 30000;

// Helper function to build full URL
export const buildApiUrl = (endpoint) => {
  return `${API_URL}${endpoint}`;
};