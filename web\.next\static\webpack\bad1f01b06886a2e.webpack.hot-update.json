{"c": ["webpack"], "r": ["pages/services"], "m": ["./node_modules/@mui/material/Card/Card.js", "./node_modules/@mui/material/Card/cardClasses.js", "./node_modules/@mui/material/Card/index.js", "./node_modules/@mui/material/CardContent/CardContent.js", "./node_modules/@mui/material/CardContent/cardContentClasses.js", "./node_modules/@mui/material/CardContent/index.js", "./node_modules/@mui/material/CardMedia/CardMedia.js", "./node_modules/@mui/material/CardMedia/cardMediaClasses.js", "./node_modules/@mui/material/CardMedia/index.js", "./node_modules/@mui/material/Grid/Grid.js", "./node_modules/@mui/material/Grid/GridContext.js", "./node_modules/@mui/material/Grid/gridClasses.js", "./node_modules/@mui/material/Grid/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cjay%20prakash%5COneDrive%5CDesktop%5Cjitender%5CJitender%20Works%5CTaskTakr%5Cweb%5Cpages%5Cservices.tsx&page=%2Fservices!", "./pages/services.tsx", "__barrel_optimize__?names=<PERSON>ton,Card,CardContent,CardMedia,Container,Grid,Typography!=!./node_modules/@mui/material/index.js"]}