import React from 'react';
import { G<PERSON>, Card, CardContent, CardMedia, Typography, Container, Button } from '@mui/material';

interface Service {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  price: string;
}

const services: Service[] = [
  {
    id: '1',
    title: 'Home Cleaning',
    description: 'Professional home cleaning services for a spotless living space.',
    imageUrl: '/images/cleaning.jpg',
    price: 'Starting from $20/hr'
  },
  {
    id: '2',
    title: 'Plumbing',
    description: 'Expert plumbing services for all your repair and maintenance needs.',
    imageUrl: '/images/plumbing.jpg',
    price: 'Starting from $30/hr'
  },
  {
    id: '3',
    title: 'Electrical Work',
    description: 'Licensed electricians for installations and repairs.',
    imageUrl: '/images/electrical.jpg',
    price: 'Starting from $35/hr'
  },
  {
    id: '4',
    title: 'Gardening',
    description: 'Professional gardening and landscaping services.',
    imageUrl: '/images/gardening.jpg',
    price: 'Starting from $25/hr'
  }
];

export default function Services() {
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Our Services
      </Typography>
      <Grid container spacing={4}>
        {services.map((service) => (
          <Grid item key={service.id} xs={12} sm={6} md={4}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="img"
                height="200"
                image={service.imageUrl}
                alt={service.title}
                sx={{ objectFit: 'cover' }}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h5" component="h2">
                  {service.title}
                </Typography>
                <Typography>
                  {service.description}
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mt: 2 }}>
                  {service.price}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  sx={{ mt: 2 }}
                  onClick={() => console.log(`Booking service: ${service.title}`)}
                >
                  Book Now
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}