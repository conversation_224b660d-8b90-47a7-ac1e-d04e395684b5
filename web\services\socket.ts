import io, { Socket } from 'socket.io-client';
import { store } from '../store';

// Environment-based Socket configuration
const getSocketUrl = () => {
  if (process.env.NODE_ENV === 'production') {
    return process.env.NEXT_PUBLIC_SOCKET_URL || 'https://backend.tasktakr.in';
  } else {
    return process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5099';
  }
};

class SocketService {
  private socket: Socket | null = null;
  private static instance: SocketService;

  private constructor() {}

  static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  connect() {
    if (!this.socket) {
      const socketUrl = getSocketUrl();
      this.socket = io(socketUrl, {
        query: {
          token: store.getState().auth?.token
        }
      });

      this.socket.on('connect', () => {
        console.log('Socket connected');
      });

      this.socket.on('disconnect', () => {
        console.log('Socket disconnected');
      });

      this.socket.on('error', (error) => {
        console.error('Socket error:', error);
      });
    }
    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Join a specific room (e.g., for booking updates)
  joinRoom(roomId: string) {
    if (this.socket) {
      this.socket.emit('join_room', roomId);
    }
  }

  // Leave a specific room
  leaveRoom(roomId: string) {
    if (this.socket) {
      this.socket.emit('leave_room', roomId);
    }
  }

  // Subscribe to provider location updates
  onProviderLocationUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('provider_location_update', callback);
    }
  }

  // Subscribe to service status updates
  onServiceStatusUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('service_status_update', callback);
    }
  }
}

export default SocketService.getInstance();